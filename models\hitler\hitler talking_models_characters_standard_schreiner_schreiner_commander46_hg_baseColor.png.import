[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://dbthmx3ylqq7d"
path.s3tc="res://.godot/imported/hitler talking_models_characters_standard_schreiner_schreiner_commander46_hg_baseColor.png-57f13b6ad771aa45958a2dafb991370d.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "e8abc8be576d1d7d482a4c873b274e7a"
}

[deps]

source_file="res://models/hitler/hitler talking_models_characters_standard_schreiner_schreiner_commander46_hg_baseColor.png"
dest_files=["res://.godot/imported/hitler talking_models_characters_standard_schreiner_schreiner_commander46_hg_baseColor.png-57f13b6ad771aa45958a2dafb991370d.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
