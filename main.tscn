[gd_scene load_steps=10 format=3 uid="uid://dpd6ki7rbw14u"]

[ext_resource type="PackedScene" uid="uid://fuwugt1q7bag" path="res://models/cabin/1930s_train_cabin.glb" id="1_ig7tw"]
[ext_resource type="PackedScene" uid="uid://cde0jseiaxbpy" path="res://character/characters/cleopatra.tscn" id="2_0xm2m"]
[ext_resource type="PackedScene" uid="uid://b1xtwb78akjoo" path="res://character/characters/einstein.tscn" id="3_h2yge"]
[ext_resource type="PackedScene" uid="uid://decijjam2nka4" path="res://character/characters/ibn khaldun.tscn" id="4_1bvp3"]
[ext_resource type="Script" uid="uid://cucts4nuat6fi" path="res://character spawner/character_spawner.gd" id="5_lquwl"]
[ext_resource type="Script" uid="uid://c55dnuaeonc23" path="res://addons/GD-Sync/Scripts/Types/NodeInstantiator.gd" id="6_7mycd"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_ig7tw"]
sky_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)
ground_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)

[sub_resource type="Sky" id="Sky_0xm2m"]
sky_material = SubResource("ProceduralSkyMaterial_ig7tw")

[sub_resource type="Environment" id="Environment_h2yge"]
background_mode = 1
background_color = Color(0.118581, 0.118581, 0.118581, 1)
sky = SubResource("Sky_0xm2m")
tonemap_mode = 2
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0, 0, 0, 1)

[node name="main" type="Node3D"]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_h2yge")

[node name="Sketchfab_Scene" parent="." instance=ExtResource("1_ig7tw")]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, -1.16622, 0, 0)

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.147675, 0.385796, -0.0509726)
light_color = Color(0.984314, 0.984314, 0.701961, 1)
light_energy = 0.5
light_indirect_energy = 7.909
light_volumetric_fog_energy = 13.0
omni_range = 0.316237

[node name="Character Spawner" type="Node3D" parent="."]
script = ExtResource("5_lquwl")
player_scenes = Array[PackedScene]([ExtResource("2_0xm2m"), ExtResource("3_h2yge"), ExtResource("4_1bvp3")])

[node name="0" type="Node3D" parent="Character Spawner"]
transform = Transform3D(0.0276599, 0, -0.999617, 0, 1, 0, 0.999617, 0, 0.0276599, 0.342488, 0.0871184, 0.0987347)

[node name="1" type="Node3D" parent="Character Spawner"]
transform = Transform3D(-0.12703, 0, 0.991899, 0, 1, 0, -0.991899, 0, -0.12703, -0.148088, 0.11931, -0.14391)

[node name="2" type="Node3D" parent="Character Spawner"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -0.167969, 0.113177, -0.00885969)

[node name="CharacterInstantiator" type="Node" parent="Character Spawner"]
script = ExtResource("6_7mycd")
target_location = NodePath("")
scene = ExtResource("2_0xm2m")
replicate_on_join = true
sync_starting_changes = true
excluded_properties = PackedStringArray()
metadata/_custom_type_script = "uid://c55dnuaeonc23"

[node name="CharacterInstantiator2" type="Node" parent="Character Spawner"]
script = ExtResource("6_7mycd")
target_location = NodePath("")
scene = ExtResource("3_h2yge")
replicate_on_join = true
sync_starting_changes = true
excluded_properties = PackedStringArray()
metadata/_custom_type_script = "uid://c55dnuaeonc23"

[node name="CharacterInstantiator3" type="Node" parent="Character Spawner"]
script = ExtResource("6_7mycd")
target_location = NodePath("")
scene = ExtResource("4_1bvp3")
replicate_on_join = true
sync_starting_changes = true
excluded_properties = PackedStringArray()
metadata/_custom_type_script = "uid://c55dnuaeonc23"
