[gd_scene load_steps=9 format=3 uid="uid://dpd6ki7rbw14u"]

[ext_resource type="PackedScene" uid="uid://fuwugt1q7bag" path="res://models/cabin/1930s_train_cabin.glb" id="1_ig7tw"]
[ext_resource type="PackedScene" uid="uid://ds77e6kvlggwn" path="res://ai character/ai_character.tscn" id="1_lquwl"]
[ext_resource type="PackedScene" uid="uid://bkctjdhc5en0l" path="res://models/cleopatra/cleopatra.glb" id="2_0xm2m"]
[ext_resource type="PackedScene" uid="uid://dflvk3qowdsc2" path="res://models/hitler/hitler.glb" id="3_h2yge"]
[ext_resource type="PackedScene" uid="uid://hlb7h0h7srrq" path="res://player/player.tscn" id="4_1bvp3"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_ig7tw"]
sky_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)
ground_horizon_color = Color(0.662243, 0.671743, 0.686743, 1)

[sub_resource type="Sky" id="Sky_0xm2m"]
sky_material = SubResource("ProceduralSkyMaterial_ig7tw")

[sub_resource type="Environment" id="Environment_h2yge"]
background_mode = 1
background_color = Color(0.118581, 0.118581, 0.118581, 1)
sky = SubResource("Sky_0xm2m")
tonemap_mode = 2
glow_enabled = true
fog_enabled = true
fog_light_color = Color(0, 0, 0, 1)

[node name="main" type="Node3D"]

[node name="cleopatra" parent="." instance=ExtResource("1_lquwl")]
targeted_char = "hitler"

[node name="hitler" parent="." instance=ExtResource("1_lquwl")]
targeted_char = "cleopatra"
char_name = "hitler"

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_h2yge")

[node name="Sketchfab_Scene" parent="." instance=ExtResource("1_ig7tw")]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, -1.16622, 0, 0)

[node name="OmniLight3D" type="OmniLight3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.147675, 0.385796, -0.0509726)
light_color = Color(0.984314, 0.984314, 0.701961, 1)
light_energy = 0.5
light_indirect_energy = 7.909
light_volumetric_fog_energy = 13.0
omni_range = 0.316237

[node name="Models" type="Node3D" parent="."]

[node name="hitler" parent="Models" instance=ExtResource("3_h2yge")]
transform = Transform3D(-1.31134e-10, 0, -0.003, 0, 0.003, 0, 0.003, 0, -1.31134e-10, 0.412318, 0.0414224, -0.141199)

[node name="cleopatra" parent="Models" instance=ExtResource("2_0xm2m")]
transform = Transform3D(-7.86805e-09, 0, -0.18, 0, 0.18, 0, 0.18, 0, -7.86805e-09, 0.377524, 0.051387, 0.0542534)

[node name="Skeleton3D" parent="Models/cleopatra/Armature" index="0"]
bones/0/position = Vector3(-0.519089, 6.68566, -51.9611)
bones/0/rotation = Quaternion(-0.761959, -0.0389568, 0.0567599, 0.643955)
bones/1/position = Vector3(-5.96046e-08, 6.51736, 0.0391218)
bones/1/rotation = Quaternion(0.178329, -0.00403233, -0.0322772, 0.983433)
bones/2/position = Vector3(-4.17232e-07, 7.60373, -6.55651e-07)
bones/2/rotation = Quaternion(0.0196826, -0.000950329, -0.0032934, 0.9998)
bones/3/position = Vector3(1.31547e-07, 8.68996, -6.10932e-07)
bones/3/rotation = Quaternion(0.0196081, -0.000907132, -0.00348708, 0.999801)
bones/4/position = Vector3(4.17233e-07, 9.77623, 3.45029e-06)
bones/4/rotation = Quaternion(0.0094621, 0.00419689, 0.000654048, 0.999946)
bones/5/position = Vector3(-8.34465e-07, 30.0605, 1.23887)
bones/5/rotation = Quaternion(-0.147778, 0.030861, 0.0279565, 0.988144)
bones/6/position = Vector3(-3.57628e-07, 40.6236, 1.6742)
bones/7/position = Vector3(6.07987, 1.02858, -0.55338)
bones/7/rotation = Quaternion(0.0472568, -0.0523205, -0.622899, 0.779119)
bones/8/position = Vector3(-9.91635e-06, 10.5047, 9.87926e-07)
bones/8/rotation = Quaternion(0.552812, 0.460646, -0.287414, 0.632137)
bones/9/position = Vector3(8.3279e-07, 10.608, 1.95512e-06)
bones/9/rotation = Quaternion(0.052413, 0.00747985, 0.474915, 0.878438)
bones/10/position = Vector3(-7.9046e-06, 21.767, -3.08368e-07)
bones/10/rotation = Quaternion(-0.10291, 0.26257, 0.150216, 0.947577)
bones/11/position = Vector3(-3.87829, 0.177955, 0.252842)
bones/11/rotation = Quaternion(0.158955, 0.299707, 0.0312491, 0.940177)
bones/12/position = Vector3(-1.12975, 2.90019, 4.20895e-07)
bones/12/rotation = Quaternion(0.178057, 0.0626766, -0.039536, 0.981226)
bones/13/position = Vector3(-0.63132, 6.46002, 1.60574e-06)
bones/13/rotation = Quaternion(-0.00349098, 0.0607854, 0.195154, 0.978881)
bones/14/position = Vector3(1.76108, 5.21965, -3.27825e-06)
bones/15/position = Vector3(-2.50407, 5.96522, 0.141167)
bones/15/rotation = Quaternion(0.460109, -0.0661433, -0.155155, 0.871695)
bones/16/position = Vector3(-0.014991, 5.34609, -6.59161e-06)
bones/16/rotation = Quaternion(0.282366, 0.000285376, -0.0384347, 0.958537)
bones/17/position = Vector3(0.00290811, 4.40707, 4.25287e-06)
bones/17/rotation = Quaternion(0.113231, -0.00018805, -0.0148858, 0.993457)
bones/18/position = Vector3(0.0120919, 3.95843, -6.1281e-07)
bones/19/position = Vector3(2.50406, 12.6399, 0.132206)
bones/19/rotation = Quaternion(0.376481, -0.0705822, -0.134675, 0.913862)
bones/20/position = Vector3(0.0148153, 4.44974, 4.41743e-06)
bones/20/rotation = Quaternion(0.269737, -0.00022101, -0.0454318, 0.961862)
bones/21/position = Vector3(-0.00689721, 3.95959, 7.69196e-07)
bones/21/rotation = Quaternion(0.180059, 0.000433061, -0.0290885, 0.983226)
bones/22/position = Vector3(-0.00790834, 3.09827, -1.2815e-06)
bones/23/position = Vector3(-6.07987, 1.03585, 0.658359)
bones/23/rotation = Quaternion(0.150946, 0.143561, 0.640066, 0.739541)
bones/24/position = Vector3(2.48551e-06, 10.5047, -1.63823e-06)
bones/24/rotation = Quaternion(-0.554004, 0.528288, -0.119392, -0.632248)
bones/25/position = Vector3(-1.7037e-06, 10.9544, -4.43532e-06)
bones/25/rotation = Quaternion(-0.141647, -0.219536, -0.290371, 0.920557)
bones/26/position = Vector3(6.81689e-07, 22.0035, 4.54058e-07)
bones/26/rotation = Quaternion(-0.0391986, 0.105896, -0.147323, 0.982622)
bones/27/position = Vector3(1.8208, 5.32803, -0.347948)
bones/27/rotation = Quaternion(0.125746, -0.068847, -0.468084, 0.871978)
bones/28/position = Vector3(-2.56122, 3.71587, -2.74988e-06)
bones/28/rotation = Quaternion(0.184132, 0.0268512, 0.665228, 0.723081)
bones/29/position = Vector3(1.47215, 2.79415, -1.90137e-06)
bones/29/rotation = Quaternion(0.139715, -0.0205113, -0.097688, 0.985148)
bones/30/position = Vector3(1.08907, 6.42669, 6.4373e-06)
bones/31/position = Vector3(3.24658, 19.6841, 0.071953)
bones/31/rotation = Quaternion(0.413553, 0.221593, 0.263451, 0.84289)
bones/32/position = Vector3(0.0261507, 0.162909, 1.66623e-06)
bones/32/rotation = Quaternion(0.268028, 0.0240755, 0.060826, 0.961188)
bones/33/position = Vector3(-0.0931063, 2.31946, 7.99282e-06)
bones/33/rotation = Quaternion(0.133051, 0.00207211, 0.11919, 0.983914)
bones/34/position = Vector3(0.0669469, 2.44873, 9.41753e-06)
bones/35/position = Vector3(-3.24658, 18.6861, 0.477202)
bones/35/rotation = Quaternion(0.398754, 0.0872098, 0.0992184, 0.907494)
bones/36/position = Vector3(0.00796413, 3.06955, -5.61931e-06)
bones/36/rotation = Quaternion(0.230824, 0.000797868, 0.0112459, 0.97293)
bones/37/position = Vector3(-0.0363445, 1.74694, -1.23602e-06)
bones/37/rotation = Quaternion(0.0474872, 0.00126511, 0.0203033, 0.998665)
bones/38/position = Vector3(0.0283804, 2.68169, 2.25008e-06)
bones/39/position = Vector3(11.0472, -3.62076, -1.03429)
bones/39/rotation = Quaternion(0.114695, 0.672755, 0.719916, -0.12636)
bones/40/position = Vector3(-2.23116e-06, 41.3457, -2.43668e-07)
bones/40/rotation = Quaternion(-0.734562, 0.0301589, -0.151993, 0.660611)
bones/41/position = Vector3(-1.1964e-06, 43.5815, -8.22472e-06)
bones/41/rotation = Quaternion(0.592772, -0.0989254, 0.0574589, 0.797204)
bones/42/position = Vector3(8.31129e-07, 19.6989, -5.62943e-07)
bones/42/rotation = Quaternion(0.0380491, 0.000727411, 0.0191663, 0.999092)
bones/43/position = Vector3(5.66243e-07, 8.95168, 4.76838e-07)
bones/44/position = Vector3(-11.0472, -3.62075, -1.22814)
bones/44/rotation = Quaternion(-0.0823633, 0.689917, 0.712206, 0.0999679)
bones/45/position = Vector3(1.80153e-06, 41.3468, 1.04073e-06)
bones/45/rotation = Quaternion(-0.770095, 0.061247, 0.0319576, 0.634177)
bones/46/position = Vector3(5.81887e-07, 43.7949, 5.0556e-07)
bones/46/rotation = Quaternion(0.637506, 0.095308, -0.0706418, 0.761257)
bones/47/position = Vector3(-1.95693e-06, 20.7686, 2.89155e-06)
bones/47/rotation = Quaternion(0.0397183, -0.000408173, -0.0144437, 0.999106)
bones/48/position = Vector3(5.66244e-07, 9.73716, 1.43051e-06)

[node name="AnimationPlayer" parent="Models/cleopatra" index="1"]
autoplay = "Armature|mixamo_com|Layer0_001"

[node name="Player" parent="." instance=ExtResource("4_1bvp3")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.0688633, 0.270601, -0.118611)

[editable path="Models/hitler"]
[editable path="Models/cleopatra"]
