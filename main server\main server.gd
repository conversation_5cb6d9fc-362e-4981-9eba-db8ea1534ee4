extends Node

var speak_time_per_word := 0.4

var no_of_characters = 0

var characters = {
}

var char_references: Dictionary[String, Character] = {

}

func start_game_loop():
	for ai_char in get_tree().get_nodes_in_group("ai character"):
		char_references[ai_char.char_name] = ai_char
	for human_char in get_tree().get_nodes_in_group("human character"):
		char_references[human_char.char_name] = human_char
	
	for c in char_references.keys():
		if not char_references[c].is_main_player:
			if char_references[c].is_ai_character:
				var question_generated = await char_references[c].generate_question_for(char_references[c].targeted_char)
				print(c + ": " + question_generated)
				await Player2.speak(question_generated, char_references[c].male_voice)
				char_references[c].talk_animation()
				await get_tree().create_timer(len(question_generated.split(" ")) * speak_time_per_word).timeout
				char_references[c].idle_animation()

				var targeted_char = char_references[char_references[c].targeted_char]
				if targeted_char is Character:
					var answer_generated = await targeted_char.interpret_question(question_generated)
					print(targeted_char.char_name + ": " + answer_generated)
					await Player2.speak(answer_generated, targeted_char.male_voice)
					targeted_char.talk_animation()
					await get_tree().create_timer(len(answer_generated.split(" ")) * speak_time_per_word).timeout
					targeted_char.idle_animation()
					print("\n")
				else:
					targeted_char.can_start_speaking = true
					print("it's your turn to speak now")
					await targeted_char.speech_complete
					targeted_char.can_start_speaking = false
					print(targeted_char.name + ": " + targeted_char.speech_res)
					characters[targeted_char.name] = targeted_char.speech_res
			else:
				pass
				# TODO: wait for the other human players to speak
		else:
			char_references[c].can_start_speaking = true
			print("it's your turn to speak now")
			await char_references[c].speech_complete
			char_references[c].can_start_speaking = false
			var question_generated = char_references[c].speech_res
			print(c + ": " + question_generated)

			await Player2.speak(question_generated, char_references[c].male_voice)
			char_references[c].talk_animation()
			await get_tree().create_timer(len(question_generated.split(" ")) * speak_time_per_word).timeout
			char_references[c].idle_animation()
			char_references[c].speech_res = ""

			var targeted_char = char_references[char_references[c].targeted_char]
			if targeted_char is Character:
				var answer_generated = await targeted_char.interpret_question(question_generated)
				print(targeted_char.char_name + ": " + answer_generated)
				await Player2.speak(answer_generated, targeted_char.male_voice)
				targeted_char.talk_animation()
				await get_tree().create_timer(len(answer_generated.split(" ")) * speak_time_per_word).timeout
				targeted_char.idle_animation()
				print("\n")
			else:
				targeted_char.can_start_speaking = true
				print("it's your turn to speak now")
				await targeted_char.speech_complete
				targeted_char.can_start_speaking = false
				print(targeted_char.name + ": " + targeted_char.speech_res)
				characters[targeted_char.name] = targeted_char.speech_res

				await Player2.speak(targeted_char.speech_res, targeted_char.male_voice)
				targeted_char.talk_animation()
				await get_tree().create_timer(len(targeted_char.speech_res.split(" ")) * speak_time_per_word).timeout
				targeted_char.idle_animation()
				targeted_char.speech_res = ""

	for c in char_references.keys():
		if char_references[c] is Character:
			var suspect = await char_references[c].decide_who_is_human()
			print(c + " suspects " + suspect + " is human")
	
