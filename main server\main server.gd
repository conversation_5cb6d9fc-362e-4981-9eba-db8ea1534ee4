extends Node

var characters = {
}

var char_references: Dictionary[String, AICharacter] = {

}


func _ready():
	for ai_char in get_tree().get_nodes_in_group("ai character"):
		if ai_char is AICharacter:
			char_references[ai_char.char_name] = ai_char
	for c in char_references.keys():
		var question_generated = await char_references[c].generate_question_for(char_references[c].targeted_char)
		print(c + ": " + question_generated)
		var targeted_char = char_references[char_references[c].targeted_char]
		var answer_generated = await targeted_char.interpret_question(question_generated)
		print(targeted_char.char_name + ": " + answer_generated)
		print("\n")
	
	for c in char_references.keys():
		var suspect = await char_references[c].decide_who_is_human()
		print(c + " suspects " + suspect + " is human")
	
