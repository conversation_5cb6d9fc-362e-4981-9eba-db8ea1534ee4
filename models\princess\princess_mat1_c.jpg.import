[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://cyuio5vk554iq"
path.s3tc="res://.godot/imported/princess_mat1_c.jpg-f27cad89c8b1acc1e6d20f176e044da0.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "fdbcdc0fe6b3cfc1fc080004751bcc63"
}

[deps]

source_file="res://models/princess/princess_mat1_c.jpg"
dest_files=["res://.godot/imported/princess_mat1_c.jpg-f27cad89c8b1acc1e6d20f176e044da0.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
