class_name AICharacter extends Node3D

@export var targeted_char = ""
@export var char_name = "cleopatra"

var char_history = []

var personality = ["sarcastic", "logical", "paranoid", "cheerful"]

func interpret_question(question: String):
	var prompt = """
You are %s (the historical figure) with the personality of %s, and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are given a question: %s
You must answer the question in a way that is consistent with your character in order to proof that you are an AI and not a human pretending to be the historical figure.
You should only return your answer in the form of a speech, so simple text only.
	""" % [char_name, personality.pick_random(), question]
	Server.characters[char_name] = await Player2.create_chat_completion(char_history, prompt)
	return Server.characters[char_name]

func generate_question_for(target_char: String):
	var prompt = """
You are %s (the historical figure), and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are required to ask %s a question to see if they are an AI or a human.
You should only return the speech you want to say, so simple text only.
""" % [char_name, target_char]
	return await Player2.create_chat_completion(char_history, prompt)

func decide_who_is_human():
	var prompt = """
You are %s (the historical figure), and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are required to decide who is the human pretending to be the historical figure and who is the AI.
Here's a list of everyone who's in the cabin and what they have said respectively:
%s
Based on it, decide who is the human, and you cannot suspect yourself.
return the name of the human only.
""" % [char_name, str(Server.characters)]
	return await Player2.create_chat_completion(char_history, prompt)