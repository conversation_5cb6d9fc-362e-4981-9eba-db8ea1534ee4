extends Node

@onready var http_request: HTTPRequest = HTTPRequest.new()

var server_ok := false
var game_id = 1

# Rate limiting and queue management for server with queue limit of 2
var last_request_time = 0.0
var min_request_interval = 1.0  # 1 seconds between requests to avoid queue buildup
var active_requests = 0  # Track how many requests are currently active
var max_concurrent_requests = 1  # Only allow 1 request at a time to be safe

func _ready():
	add_child(http_request)
	server_ok = await get_server_health()
	if server_ok:
		push_warning("AI Server is ready!")
		# Test the API with the correct format
		var test_result = await create_chat_completion([{"role": "user", "content": "what is your name?"}], "Your name is <PERSON>")
		if test_result != "":
			push_warning("Test successful: " + test_result)
		else:
			push_error("Initial test failed - check server queue status")

func get_server_health() -> bool:
	http_request.request("http://127.0.0.1:4315/v1/health")
	var result = await http_request.request_completed
	var json = _process_result(result)
	return json.has("client_version") and json["client_version"] is String

# Queue-aware implementation to handle server queue limit of 2
func create_chat_completion(character_history: Array = [], additional_context: String = ""):
	# Wait for concurrent request limit
	await _wait_for_request_slot()

	# Mark request as active
	active_requests += 1

	var result = ""
	var max_retries = 3

	for attempt in range(max_retries):
		# Wait for rate limiting
		await _wait_for_rate_limit()

		# Build messages array according to API documentation
		var messages = []

		# Add system context first if provided
		if additional_context != "":
			messages.append({"role": "system", "content": additional_context})

		# Add character history
		for message in character_history:
			messages.append(message)

		# Create request body exactly as shown in API documentation
		var request_body = {
			"messages": messages,
			"stream": false
		}

		# Convert to JSON string properly
		var json_string = JSON.stringify(request_body)
		var headers = ["Content-Type: application/json", "player2-game-key: monarch-of-the-machine"]

		push_warning("Making AI request attempt %d/%d with %d messages" % [attempt + 1, max_retries, messages.size()])

		# Make the request
		http_request.request("http://127.0.0.1:4315/v1/chat/completions", headers, HTTPClient.METHOD_POST, json_string)
		var http_result = await http_request.request_completed

		# Update last request time for rate limiting
		last_request_time = Time.get_unix_time_from_system()

		# Process the result
		var json = _process_result(http_result)

		# Check if we got a valid response
		if json.has("choices") and json["choices"].size() > 0:
			var choice = json["choices"][0]
			if choice.has("message") and choice["message"].has("content"):
				result = choice["message"]["content"]
				break  # Success, exit retry loop

		# Check if it was a 429 error (queue full)
		var response_code = http_result[1]
		if response_code == 429:
			if attempt < max_retries - 1:
				var wait_time = (attempt + 1) * 2.0  # Exponential backoff: 5s, 10s, 15s
				push_warning("Queue full (429), waiting %.1f seconds before retry %d/%d" % [wait_time, attempt + 2, max_retries])
				await get_tree().create_timer(wait_time).timeout
			else:
				push_error("Failed after %d attempts due to server queue being full. Consider upgrading Patron tier." % max_retries)
		else:
			# Other error, don't retry
			break

	# Mark request as completed
	active_requests -= 1

	if result == "":
		push_error("AI request failed after all retry attempts")

	return result


func listen() -> void:
	# Create request body for TTS API
	var request_body = {
		"timeout": 30
	}

	# Convert to JSON string properly
	var json_string = JSON.stringify(request_body)
	var headers = ["Content-Type: application/json", "player2-game-key: guess-the-human"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/stt/start", headers, HTTPClient.METHOD_POST, json_string)
	var http_result = await http_request.request_completed

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429)")

func stop_listening():
	# Wait for concurrent request limit
	var res = {}

	var headers = ["Content-Type: application/json", "player2-game-key: temp_key"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/stt/stop", headers, HTTPClient.METHOD_POST)
	var http_result = await http_request.request_completed

	# Update last request time for rate limiting

	# Process the result for error logging
	res = _process_result(http_result)

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429)")
	# Mark request as completed
	return res["text"]


func speak(text: String, male_voice: bool) -> void:
	# Create request body for TTS API
	var request_body = {
		"audio_format": "mp3",
		"play_in_app": true,
		"speed": 1,
		"text": text,
		"voice_gender": "male" if male_voice else "female",
		"voice_ids": [
			"01955d76-ed5b-74af-a2be-9302077075b8"
		] if male_voice else [
			"01955d76-ed5b-73e0-a88d-cbeb3c5b499d",
		],
		"voice_language": "en_US"
	}

	# Convert to JSON string properly
	var json_string = JSON.stringify(request_body)
	var headers = ["Content-Type: application/json", "player2-game-key: temp_key"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/tts/speak", headers, HTTPClient.METHOD_POST, json_string)
	var http_result = await http_request.request_completed

	# Update last request time for rate limiting

	# Process the result for error logging
	_process_result(http_result)

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429), waiting %.1f seconds before retry %d/%d")

# Wait for available request slot to prevent queue overflow
func _wait_for_request_slot():
	while active_requests >= max_concurrent_requests:
		push_warning("Waiting for request slot (currently %d/%d active)" % [active_requests, max_concurrent_requests])
		await get_tree().create_timer(1.0).timeout

# Rate limiting with better messaging
func _wait_for_rate_limit():
	var current_time = Time.get_unix_time_from_system()
	var time_since_last = current_time - last_request_time

	if time_since_last < min_request_interval:
		var wait_time = min_request_interval - time_since_last
		push_warning("Rate limiting: waiting %.1f seconds" % wait_time)
		await get_tree().create_timer(wait_time).timeout

func _process_result(result: Array):
	var response_code = result[1]
	var body_bytes = result[3]
	var body_text = body_bytes.get_string_from_utf8()

	# Enhanced error handling with proper push_error/push_warning
	if response_code != 200 and response_code != 201:
		if response_code == 429:
			push_error("ERROR 429: Server queue full! Response: %s" % body_text)
		elif response_code == 401:
			push_error("ERROR 401: Authentication required in Player2 App")
		elif response_code == 402:
			push_error("ERROR 402: Insufficient credits")
		elif response_code == 500:
			push_error("ERROR 500: Internal server error")
		else:
			push_error("HTTP Error %d: %s" % [response_code, body_text])
		return {}

	var parsed = JSON.parse_string(body_text)
	if parsed == null:
		push_error("JSON Parse Error. Response body: %s" % body_text)
		return {}

	return parsed