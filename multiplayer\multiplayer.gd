extends Node

var server_connected = false

func _ready():
	GDSync.connected.connect(connected)
	GDSync.connection_failed.connect(connection_failed)

	GDSync.lobby_created.connect(lobby_created)
	GDSync.lobby_creation_failed.connect(lobby_creation_failed)

	GDSync.lobby_joined.connect(lobby_joined)
	GDSync.lobby_join_failed.connect(lobby_joined_failed)

	GDSync.start_multiplayer()

func connected():
	print("connected!")
	server_connected = true

func connection_failed(error: int):
	match error:
		ENUMS.CONNECTION_FAILED.INVALID_PUBLIC_KEY:
			push_error("Invalid public key.")
			GDSync.start_multiplayer()
		ENUMS.CONNECTION_FAILED.TIMEOUT:
			push_error("Connection timed out.")

func lobby_created(lobby_name):
	print("created lobby", lobby_name)

	GDSync.lobby_join(lobby_name)

func lobby_creation_failed(lobby_name, error):
	print("failed to create lobby", lobby_name)

	if error == ENUMS.LOBBY_CREATION_ERROR.LOBBY_ALREADY_EXISTS:
		GDSync.lobby_join(lobby_name)

func lobby_joined(lobby_name):
	print("Joined Lobby ", lobby_name)

func lobby_joined_failed(lobby_name, _error):
	print("Failed to join lobby", lobby_name)

func create_lobby(lobby_name):
	if server_connected:
		GDSync.lobby_create(lobby_name)

func join_lobby(lobby_name):
	if server_connected:
		GDSync.lobby_join(lobby_name)