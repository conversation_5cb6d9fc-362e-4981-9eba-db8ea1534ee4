[gd_scene load_steps=3 format=3 uid="uid://b1xtwb78akjoo"]

[ext_resource type="Script" uid="uid://btwugcpubrukk" path="res://character/character.gd" id="1_81s1x"]
[ext_resource type="PackedScene" uid="uid://bewhp2wxhoe5a" path="res://models/einstein/einstein.glb" id="2_81s1x"]

[node name="Einstein" type="Node3D" groups=["ai character"]]
script = ExtResource("1_81s1x")
char_name = "einstein"
male_voice = true
talking_animation_name = "Armature|mixamo_com|Layer0_006"
idle_animation_name = "Armature|mixamo_com|Layer0_001"

[node name="einstein" parent="." instance=ExtResource("2_81s1x")]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)

[node name="Skeleton3D" parent="einstein/Armature" index="0"]
bones/0/position = Vector3(-0.689882, 7.9996, -40.8607)
bones/0/rotation = Quaternion(-0.761885, -0.0399706, 0.0566578, 0.64399)
bones/1/position = Vector3(1.78814e-07, 11.4584, -0.57435)
bones/1/rotation = Quaternion(0.150655, -0.00365429, -0.0315135, 0.988077)
bones/2/position = Vector3(5.96046e-08, 13.385, 2.21282e-06)
bones/2/rotation = Quaternion(0.0196826, -0.000893338, -0.0033093, 0.9998)
bones/3/position = Vector3(4.76837e-07, 15.2971, 9.31323e-07)
bones/3/rotation = Quaternion(0.0196082, -0.000846822, -0.00350222, 0.999801)
bones/4/position = Vector3(3.27826e-07, 17.2092, -2.62943e-06)
bones/4/rotation = Quaternion(0.0374958, 0.00417691, 0.00077147, 0.999288)
bones/5/position = Vector3(4.17233e-07, 7.98542, 1.80606)
bones/5/rotation = Quaternion(-0.147778, 0.030861, 0.0279566, 0.988144)
bones/6/position = Vector3(0, 26.594, 6.01478)
bones/7/position = Vector3(7.23589, 14.2158, -0.0929087)
bones/7/rotation = Quaternion(-0.650373, -0.376067, 0.490343, -0.44176)
bones/8/position = Vector3(-1.02333e-07, 15.5553, -4.8925e-07)
bones/8/rotation = Quaternion(0.3283, -0.0319538, 0.193205, 0.924051)
bones/9/position = Vector3(4.0821e-07, 23.7468, 3.73472e-06)
bones/9/rotation = Quaternion(-0.110781, 0.0404543, 0.527922, 0.841064)
bones/10/position = Vector3(-4.8573e-06, 29.4482, -8.24735e-06)
bones/10/rotation = Quaternion(-0.219454, 0.296632, 0.0119856, 0.929358)
bones/11/position = Vector3(1.99067e-06, 4.98936, 1.0087e-05)
bones/11/rotation = Quaternion(0.472094, 0.00951368, -0.0161806, 0.881348)
bones/12/position = Vector3(-9.53656e-07, 4.96939, -7.74887e-07)
bones/12/rotation = Quaternion(0.236144, -0.000992715, -0.0288442, 0.971289)
bones/13/position = Vector3(2.00376, 6.14103, 0.840849)
bones/13/rotation = Quaternion(0.0812468, -0.00058718, -0.011466, 0.996628)
bones/14/position = Vector3(-1.71316, 3.54616, 3.98496)
bones/15/position = Vector3(-7.2359, 14.2216, -0.206894)
bones/15/rotation = Quaternion(0.675663, -0.346929, 0.495501, 0.421425)
bones/16/position = Vector3(3.79899e-07, 15.5553, 1.66676e-05)
bones/16/rotation = Quaternion(0.253406, 0.0449406, -0.327044, 0.90929)
bones/17/position = Vector3(-1.54345e-06, 23.7568, -7.15252e-06)
bones/17/rotation = Quaternion(-0.120946, -0.0189065, -0.371503, 0.920326)
bones/18/position = Vector3(8.07279e-07, 29.4151, 1.46631e-05)
bones/18/rotation = Quaternion(0.0160689, -0.316289, -0.111703, 0.941926)
bones/19/position = Vector3(1.19804e-05, 1.35211, 2.56994e-05)
bones/19/rotation = Quaternion(-0.0400303, 0.804656, -0.572629, -0.151731)
bones/20/position = Vector3(1.19209e-06, 5.0117, 9.53676e-07)
bones/20/rotation = Quaternion(0.256641, 0.00105372, 0.0199927, 0.966299)
bones/21/position = Vector3(0.62778, 7.36058, -3.4284)
bones/21/rotation = Quaternion(0.180066, -5.6384e-05, 0.0124674, 0.983576)
bones/22/position = Vector3(-0.676037, 3.238, -3.93658)
bones/23/position = Vector3(10.9494, -6.35209, -0.345887)
bones/23/rotation = Quaternion(0.0655779, 0.601409, 0.790775, -0.0931727)
bones/24/position = Vector3(-3.2639e-07, 49.6212, -4.81185e-06)
bones/24/rotation = Quaternion(-0.714915, 0.0367817, -0.0944034, 0.691832)
bones/25/position = Vector3(-2.96573e-07, 41.1082, -4.33214e-09)
bones/25/rotation = Quaternion(0.490942, -0.0790792, 0.106379, 0.861049)
bones/26/position = Vector3(-1.16754e-06, 17.7691, 5.11729e-06)
bones/26/rotation = Quaternion(0.323093, -0.0599123, 0.0204987, 0.944247)
bones/27/position = Vector3(-9.95584e-07, 6.84254, -1.8999e-07)
bones/28/position = Vector3(-10.9494, -6.35209, -0.494074)
bones/28/rotation = Quaternion(-0.0327026, 0.613638, 0.787084, 0.0536383)
bones/29/position = Vector3(-2.24497e-06, 49.6379, -2.67694e-06)
bones/29/rotation = Quaternion(-0.747174, 0.051672, -0.0453145, 0.661066)
bones/30/position = Vector3(2.75668e-06, 41.0931, 3.39877e-06)
bones/30/rotation = Quaternion(0.531793, 0.0992816, -0.104058, 0.834572)
bones/31/position = Vector3(1.54901e-06, 17.9817, -1.01064e-05)
bones/31/rotation = Quaternion(0.319665, 0.0584468, -0.01966, 0.945522)
bones/32/position = Vector3(6.07595e-06, 6.98063, -4.18723e-06)

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0.221947, 0.104945)

[editable path="einstein"]
