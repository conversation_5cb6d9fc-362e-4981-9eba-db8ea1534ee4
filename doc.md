# 🎮 AI Social Deduction Game

## 🧍 Models
- [x] Custom/free assets
- [ ] 12 characters with animations (historical figures)
	- [x] <PERSON>
	- [x] <PERSON>
	- [ ] <PERSON>
	- [ ] <PERSON>
	- [x] <PERSON>
	- [x] <PERSON>
	- [x] Princess <PERSON><PERSON> Queen
	- [ ] <PERSON><PERSON><PERSON>
	- [ ] Emperor Qin
	- [ ] <PERSON>
	- [x] <PERSON>
	- [ ] <PERSON><PERSON>
	- [ ] Characters are subject to change based on model availability, but it is guaranteed to be 12 characters based on historical figures.
- [x] Environment assets

## 🎮 Gameplay Logic

### 🧠 Plot Generation
- [ ] AI-generated plot/theme
  - [ ] Game master announces scenario at the beginning

### 🗣️ Turn-Based Dialogue
- [x] System for turn-taking
  - [ ] Highlight player whose turn it is
  - [ ] Countdown timer for speaking turn
- [x] Player input
  - [x] STT (Speech-to-Text) integration
- [x] AI response system
  - [x] Interpret questions
  - [x] Respond based on initial personality trait
  - [x] Output using TTS (Text-to-Speech)
- [x] AI personalities
  - [x] At least 3-5 distinct personalities (e.g. sarcastic, paranoid, logical, cheerful)
  - [x] AI picks one at the start of game

### 🗳️ Voting Phase
- [ ] After each round, all players vote on who they suspect is human
  - [ ] Click-to-vote interface
- [ ] AI logic to detect human
- [ ] Countdown timer to finalize votes

### 🏆 Endgame Conditions
- [ ] After X rounds
  - [ ] Game declares win for AI or humans (Humans win if only one AI is left)
  - [ ] End screen with:
    - [ ] Identity reveals
    - [ ] Summary of who voted for whom

## 🧑 Human Character Controls
- [x] Look around using mouse
- [x] Press Space to open mic during turn
- [ ] Vote by clicking on a character

## 🌐 Multiplayer
- [ ] Implement multiplayer using GDsync
- [ ] Players can create lobbies and send invites
- [ ] Max 3 human players per game room
- [ ] Default is 4 AI + 1 Human
	- [ ] Players can invite friends to make it 5 + 2 or 6 + 3

```

func listen() -> void:
	# Create request body for TTS API
	var request_body = {
		"timeout": 30
	}

	# Convert to JSON string properly
	var json_string = JSON.stringify(request_body)
	var headers = ["Content-Type: application/json", "player2-game-key: guess-the-human"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/stt/start", headers, HTTPClient.METHOD_POST, json_string)
	var http_result = await http_request.request_completed

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429)")

func stop_listening():
	# Wait for concurrent request limit
	var res = {}

	var headers = ["Content-Type: application/json", "player2-game-key: temp_key"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/stt/stop", headers, HTTPClient.METHOD_POST)
	var http_result = await http_request.request_completed

	# Update last request time for rate limiting

	# Process the result for error logging
	res = _process_result(http_result)

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429)")
	# Mark request as completed
	return res["text"]


func speak(text: String, male_voice: bool) -> void:
	# Create request body for TTS API
	var request_body = {
		"audio_format": "mp3",
		"play_in_app": true,
		"speed": 1,
		"text": text,
		"voice_gender": "male" if male_voice else "female",
		"voice_ids": [
			"01955d76-ed5b-74af-a2be-9302077075b8"
		] if male_voice else [
			"01955d76-ed5b-73e0-a88d-cbeb3c5b499d",
		],
		"voice_language": "en_US"
	}

	# Convert to JSON string properly
	var json_string = JSON.stringify(request_body)
	var headers = ["Content-Type: application/json", "player2-game-key: temp_key"]

	# Make the request
	http_request.request("https://127.0.0.1:4315/v1/tts/speak", headers, HTTPClient.METHOD_POST, json_string)
	var http_result = await http_request.request_completed

	# Update last request time for rate limiting

	# Process the result for error logging
	_process_result(http_result)

	# Check if we got a valid response (TTS API might return different structure)
	var response_code = http_result[1]
	if response_code == 200 or response_code == 201:
		push_warning("TTS request successful")

	# Check if it was a 429 error (queue full)
	if response_code == 429:
		push_warning("Queue full (429), waiting %.1f seconds before retry %d/%d")
```