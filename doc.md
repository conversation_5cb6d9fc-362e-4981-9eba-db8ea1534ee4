# 🎮 AI Social Deduction Game

## 🧍 Models
- [x] Custom/free assets
- [ ] 12 characters with animations (historical figures)
	- [x] <PERSON>
	- [x] <PERSON>
	- [ ] <PERSON>
	- [ ] <PERSON>
	- [x] <PERSON>
	- [x] <PERSON>
	- [x] Princess <PERSON><PERSON> Queen
	- [ ] <PERSON><PERSON><PERSON>
	- [ ] Emperor Qin
	- [ ] <PERSON>
	- [x] <PERSON>
	- [ ] <PERSON><PERSON>
	- [ ] Characters are subject to change based on model availability, but it is guaranteed to be 12 characters based on historical figures.
- [x] Environment assets

## 🎮 Gameplay Logic

### 🧠 Plot Generation
- [ ] AI-generated plot/theme
  - [ ] Game master announces scenario at the beginning

### 🗣️ Turn-Based Dialogue
- [ ] System for turn-taking
  - [ ] Highlight player whose turn it is
  - [ ] Countdown timer for speaking turn
- [ ] Player input
  - [ ] STT (Speech-to-Text) integration
- [ ] AI response system
  - [x] Interpret questions
  - [x] Respond based on initial personality trait
  - [ ] Output using TTS (Text-to-Speech)
- [x] AI personalities
  - [x] At least 3-5 distinct personalities (e.g. sarcastic, paranoid, logical, cheerful)
  - [x] AI picks one at the start of game

### 🗳️ Voting Phase
- [ ] After each round, all players vote on who they suspect is human
  - [ ] Click-to-vote interface
- [ ] AI logic to detect human
- [ ] Countdown timer to finalize votes

### 🏆 Endgame Conditions
- [ ] After X rounds
  - [ ] Game declares win for AI or humans (Humans win if only one AI is left)
  - [ ] End screen with:
    - [ ] Identity reveals
    - [ ] Summary of who voted for whom

## 🧑 Human Character Controls
- [x] Look around using mouse
- [ ] Press Space to open mic during turn
- [ ] Vote by clicking on a character

## 🌐 Multiplayer
- [ ] Implement multiplayer using GDsync
- [ ] Players can create lobbies and send invites
- [ ] Max 3 human players per game room
- [ ] Default is 4 AI + 1 Human
	- [ ] Players can invite friends to make it 5 + 2 or 6 + 3
