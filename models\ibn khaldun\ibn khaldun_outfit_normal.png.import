[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://b816p71xf8jl5"
path.s3tc="res://.godot/imported/ibn khaldun_outfit_normal.png-6479f8237b15aad40c4644a00ae151a0.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "a685b62b94f4822b5ee2559d9ad49950"
}

[deps]

source_file="res://models/ibn khaldun/ibn khaldun_outfit_normal.png"
dest_files=["res://.godot/imported/ibn khaldun_outfit_normal.png-6479f8237b15aad40c4644a00ae151a0.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=1
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=1
roughness/src_normal="res://models/ibn khaldun/ibn khaldun_outfit_normal.png"
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
