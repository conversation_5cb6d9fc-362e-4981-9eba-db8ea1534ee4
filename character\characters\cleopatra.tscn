[gd_scene load_steps=3 format=3 uid="uid://cde0jseiaxbpy"]

[ext_resource type="Script" uid="uid://btwugcpubrukk" path="res://character/character.gd" id="1_dqjo7"]
[ext_resource type="PackedScene" uid="uid://bkctjdhc5en0l" path="res://models/cleopatra/cleopatra.glb" id="2_n6agt"]

[node name="Cleopatra" type="Node3D" groups=["ai character"]]
script = ExtResource("1_dqjo7")
talking_animation_name = "Armature|mixamo_com|Layer0_002"
idle_animation_name = "Armature|mixamo_com|Layer0_001"

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0.237977, 0.0949343)

[node name="cleopatra" parent="." instance=ExtResource("2_n6agt")]
transform = Transform3D(0.2, 0, 0, 0, 0.2, 0, 0, 0, 0.2, 0, 0, 0)

[node name="Skeleton3D" parent="cleopatra/Armature" index="0"]
bones/0/position = Vector3(0.223876, 6.70326, -51.9584)
bones/0/rotation = Quaternion(-0.762923, 0.00344509, 0.0209687, 0.64614)
bones/1/position = Vector3(1.21072e-07, 6.51737, 0.0391211)
bones/1/rotation = Quaternion(0.178422, -0.00398385, -0.0323475, 0.983414)
bones/2/position = Vector3(-2.98023e-08, 7.60372, -4.65661e-08)
bones/2/rotation = Quaternion(0.0197411, -0.000885338, -0.00335212, 0.999799)
bones/3/position = Vector3(-1.66477e-07, 8.68996, -1.14272e-06)
bones/3/rotation = Quaternion(0.019743, -0.000914531, -0.00338604, 0.999799)
bones/4/position = Vector3(2.98023e-07, 9.77622, 2.07605e-06)
bones/4/rotation = Quaternion(0.00947374, 0.00420138, 0.000534051, 0.999946)
bones/5/position = Vector3(-3.27826e-07, 30.0605, 1.23887)
bones/5/rotation = Quaternion(-0.147763, 0.0306595, 0.0279255, 0.988153)
bones/6/position = Vector3(0, 40.6236, 1.6742)
bones/7/position = Vector3(6.07987, 1.02858, -0.553381)
bones/7/rotation = Quaternion(0.0472956, -0.0523937, -0.622846, 0.779154)
bones/8/position = Vector3(-1.10676e-05, 10.5047, -4.05871e-06)
bones/8/rotation = Quaternion(0.552715, 0.460643, -0.287468, 0.6322)
bones/9/position = Vector3(1.98028e-06, 10.608, -2.26021e-06)
bones/9/rotation = Quaternion(0.0524265, 0.0074793, 0.475038, 0.87837)
bones/10/position = Vector3(-4.86101e-06, 21.767, -3.17733e-06)
bones/10/rotation = Quaternion(-0.102759, 0.262515, 0.150081, 0.94763)
bones/11/position = Vector3(-3.87829, 0.17796, 0.25284)
bones/11/rotation = Quaternion(0.158826, 0.299898, 0.0311819, 0.94014)
bones/12/position = Vector3(-1.12975, 2.9002, 5.31951e-06)
bones/12/rotation = Quaternion(0.178142, 0.0627097, -0.0395738, 0.981207)
bones/13/position = Vector3(-0.63132, 6.46002, 2.02945e-06)
bones/13/rotation = Quaternion(-0.0034729, 0.0608627, 0.19508, 0.978891)
bones/14/position = Vector3(1.76108, 5.21965, -7.51018e-06)
bones/15/position = Vector3(-2.50406, 5.96523, 0.14117)
bones/15/rotation = Quaternion(0.460141, -0.066369, -0.155134, 0.871664)
bones/16/position = Vector3(-0.0149953, 5.3461, -2.00565e-07)
bones/16/rotation = Quaternion(0.282426, 0.000285151, -0.0384433, 0.958518)
bones/17/position = Vector3(0.0029031, 4.40706, -4.59537e-06)
bones/17/rotation = Quaternion(0.113294, -0.000188041, -0.0148948, 0.99345)
bones/18/position = Vector3(0.0120913, 3.95843, -1.54972e-06)
bones/19/position = Vector3(2.50406, 12.6399, 0.132201)
bones/19/rotation = Quaternion(0.376539, -0.0705895, -0.134683, 0.913836)
bones/20/position = Vector3(0.0148106, 4.44975, -6.35e-06)
bones/20/rotation = Quaternion(0.269798, -0.000220876, -0.0454417, 0.961844)
bones/21/position = Vector3(-0.00689793, 3.95959, -3.58659e-06)
bones/21/rotation = Quaternion(0.180122, 0.000433227, -0.0290983, 0.983214)
bones/22/position = Vector3(-0.00791168, 3.09827, -1.49012e-07)
bones/23/position = Vector3(-6.07987, 1.03585, 0.658357)
bones/23/rotation = Quaternion(0.151138, 0.143452, 0.640003, 0.739578)
bones/24/position = Vector3(3.56225e-06, 10.5047, 3.01648e-06)
bones/24/rotation = Quaternion(-0.554163, 0.528202, -0.119275, -0.632202)
bones/25/position = Vector3(-1.6934e-06, 10.9544, -4.22506e-07)
bones/25/rotation = Quaternion(-0.141645, -0.219537, -0.290366, 0.920559)
bones/26/position = Vector3(-2.99812e-06, 22.0035, -2.14366e-06)
bones/26/rotation = Quaternion(-0.0392764, 0.106073, -0.147264, 0.982609)
bones/27/position = Vector3(1.8208, 5.32802, -0.347942)
bones/27/rotation = Quaternion(0.125628, -0.0689512, -0.467959, 0.872054)
bones/28/position = Vector3(-2.56121, 3.71587, 1.22587e-05)
bones/28/rotation = Quaternion(0.231517, -0.0498579, 0.663775, 0.709448)
bones/29/position = Vector3(1.47215, 2.79415, -3.61947e-06)
bones/29/rotation = Quaternion(0.144311, 0.0632259, -0.132675, 0.978557)
bones/30/position = Vector3(1.08907, 6.42669, 1.3113e-05)
bones/31/position = Vector3(3.24658, 19.6841, 0.0719537)
bones/31/rotation = Quaternion(0.413642, 0.221735, 0.263314, 0.842852)
bones/32/position = Vector3(0.0261524, 0.162917, -3.75943e-06)
bones/32/rotation = Quaternion(0.26808, 0.0240716, 0.0608585, 0.961171)
bones/33/position = Vector3(-0.0931029, 2.31946, -4.29152e-08)
bones/33/rotation = Quaternion(0.133104, 0.0020725, 0.119224, 0.983903)
bones/34/position = Vector3(0.0669507, 2.44873, 8.80659e-06)
bones/35/position = Vector3(-3.24659, 18.6861, 0.477206)
bones/35/rotation = Quaternion(0.398812, 0.0872126, 0.0992283, 0.907467)
bones/36/position = Vector3(0.00796318, 3.06955, -1.64933e-06)
bones/36/rotation = Quaternion(0.230886, 0.000798472, 0.0112525, 0.972915)
bones/37/position = Vector3(-0.0363467, 1.74694, -7.06569e-06)
bones/37/rotation = Quaternion(0.0475508, 0.00126537, 0.0203094, 0.998662)
bones/38/position = Vector3(0.0283787, 2.6817, 4.88758e-06)
bones/39/position = Vector3(11.0472, -3.62076, -1.0343)
bones/39/rotation = Quaternion(0.114741, 0.672738, 0.719919, -0.126393)
bones/40/position = Vector3(-2.41223e-06, 41.3457, -2.63488e-06)
bones/40/rotation = Quaternion(-0.734513, 0.0302831, -0.151857, 0.660691)
bones/41/position = Vector3(1.04025e-06, 43.5815, 4.37507e-07)
bones/41/rotation = Quaternion(0.592766, -0.0989275, 0.0573722, 0.797214)
bones/42/position = Vector3(1.95275e-06, 19.6989, 7.6865e-07)
bones/42/rotation = Quaternion(0.0380371, 0.000729726, 0.0191691, 0.999092)
bones/43/position = Vector3(2.71946e-06, 8.95168, -1.19209e-07)
bones/44/position = Vector3(-11.0472, -3.62075, -1.22814)
bones/44/rotation = Quaternion(-0.0822922, 0.689929, 0.712197, 0.100013)
bones/45/position = Vector3(3.25636e-06, 41.3468, 6.53381e-06)
bones/45/rotation = Quaternion(-0.77011, 0.0612023, 0.0320233, 0.634161)
bones/46/position = Vector3(-2.56041e-06, 43.7949, -5.57162e-07)
bones/46/rotation = Quaternion(0.63749, 0.0954183, -0.0707448, 0.761247)
bones/47/position = Vector3(-9.66387e-06, 20.7686, 6.82881e-07)
bones/47/rotation = Quaternion(0.039907, -0.00037439, -0.0144016, 0.9991)
bones/48/position = Vector3(3.48687e-06, 9.73716, -3.57628e-07)

[editable path="cleopatra"]
