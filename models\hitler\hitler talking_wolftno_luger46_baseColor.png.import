[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://c5hg2udnspe41"
path.s3tc="res://.godot/imported/hitler talking_wolftno_luger46_baseColor.png-e64b57abd392eef4efd97acadbc99fb4.s3tc.ctex"
metadata={
"imported_formats": ["s3tc_bptc"],
"vram_texture": true
}
generator_parameters={
"md5": "e66dffc740b9dcccc63d07721ca013fd"
}

[deps]

source_file="res://models/hitler/hitler talking_wolftno_luger46_baseColor.png"
dest_files=["res://.godot/imported/hitler talking_wolftno_luger46_baseColor.png-e64b57abd392eef4efd97acadbc99fb4.s3tc.ctex"]

[params]

compress/mode=2
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=true
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=0
