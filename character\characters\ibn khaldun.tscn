[gd_scene load_steps=3 format=3 uid="uid://decijjam2nka4"]

[ext_resource type="Script" uid="uid://btwugcpubrukk" path="res://character/character.gd" id="1_11lj7"]
[ext_resource type="PackedScene" uid="uid://dki520xkp0nsx" path="res://models/ibn khaldun/ibn khaldun.glb" id="2_11lj7"]

[node name="Ibn Khaldun" type="Node3D" groups=["ai character"]]
script = ExtResource("1_11lj7")
char_name = "ibn khaldun"
male_voice = true
talking_animation_name = "Armature|mixamo_com|Layer0_006"
idle_animation_name = "Armature|mixamo_com|Layer0_001"

[node name="Camera3D" type="Camera3D" parent="."]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, 0, 0.270958, 0.141522)

[node name="ibn khaldun" parent="." instance=ExtResource("2_11lj7")]
transform = Transform3D(0.1, 0, 0, 0, 0.1, 0, 0, 0, 0.1, 0, 0, 0)

[node name="Skeleton3D" parent="ibn khaldun/Armature" index="0"]
bones/0/position = Vector3(-0.775152, 23.726, -133.562)
bones/0/rotation = Quaternion(-0.762499, 0.0220539, 0.0227072, 0.646215)
bones/1/rotation = Quaternion(0.16103, -0.0164652, -0.0457884, 0.985749)
bones/2/rotation = Quaternion(0.0197412, -0.000769434, -0.00338061, 0.999799)
bones/3/rotation = Quaternion(0.019743, -0.000797447, -0.00341551, 0.999799)
bones/4/position = Vector3(5.96046e-07, 35.5745, -3.79111e-06)
bones/4/rotation = Quaternion(0.0266827, 0.00419157, 0.000606284, 0.999635)
bones/5/rotation = Quaternion(-0.147763, 0.0306595, 0.0279255, 0.988153)
bones/6/position = Vector3(-6.55651e-07, 67.91, 25.0063)
bones/7/position = Vector3(16.434, 31.0619, -0.135164)
bones/7/rotation = Quaternion(-0.661921, -0.351955, 0.505518, -0.42713)
bones/8/position = Vector3(4.61047e-06, 34.0873, 1.94806e-05)
bones/8/rotation = Quaternion(0.321619, -0.0598498, 0.186243, 0.926441)
bones/9/position = Vector3(4.95795e-06, 48.2915, 2.51698e-06)
bones/9/rotation = Quaternion(0.0259238, -0.00108848, 0.477219, 0.878402)
bones/10/position = Vector3(3.06688e-06, 71.1579, 1.44161e-05)
bones/10/rotation = Quaternion(-0.124318, 0.241683, 0.00115998, 0.962358)
bones/11/position = Vector3(-7.11428, 6.11386, 3.07492)
bones/11/rotation = Quaternion(0.189547, 0.169721, 0.311112, 0.915683)
bones/12/position = Vector3(-0.0298271, 7.89719, -2.13287e-05)
bones/12/rotation = Quaternion(0.133557, -0.00367178, -0.241413, 0.961181)
bones/13/position = Vector3(-0.40893, 7.78537, -5.97234e-06)
bones/13/rotation = Quaternion(-0.014027, 0.0428756, 0.052376, 0.997608)
bones/14/position = Vector3(0.438734, 5.93904, -2.52724e-05)
bones/15/position = Vector3(-8.63048, 25.0245, -0.969571)
bones/15/rotation = Quaternion(0.451028, 0.00766632, -0.00403468, 0.892468)
bones/16/position = Vector3(-0.00200367, 7.32871, -4.50103e-05)
bones/16/rotation = Quaternion(0.28422, 3.89459e-05, -0.0242024, 0.958454)
bones/17/position = Vector3(9.53657e-07, 6.74787, 1.68619e-06)
bones/17/rotation = Quaternion(0.114011, -2.12379e-05, -0.00959213, 0.993433)
bones/18/position = Vector3(0.00198054, 5.85621, -1.13844e-05)
bones/19/position = Vector3(-2.43755, 25.3601, -0.666136)
bones/19/rotation = Quaternion(0.367257, -0.00507975, 0.017489, 0.929941)
bones/20/position = Vector3(0.0138111, 8.80268, -6.83302e-06)
bones/20/rotation = Quaternion(0.271888, -0.000153367, -0.0276813, 0.961931)
bones/21/position = Vector3(-0.00245094, 8.09639, -1.08512e-05)
bones/21/rotation = Quaternion(0.181517, 0.000153826, -0.0185103, 0.983214)
bones/22/position = Vector3(-0.0113478, 7.14838, 2.24113e-05)
bones/23/position = Vector3(3.28095, 25.5615, -0.700401)
bones/23/rotation = Quaternion(0.315105, 0.00508679, -0.0192041, 0.948849)
bones/24/position = Vector3(0.00408626, 7.45512, 8.00686e-06)
bones/24/rotation = Quaternion(0.187996, -2.28954e-05, -0.0203716, 0.981959)
bones/25/position = Vector3(-0.00174689, 6.97497, -4.36683e-05)
bones/25/rotation = Quaternion(0.163675, 4.95312e-05, -0.0174664, 0.98636)
bones/26/position = Vector3(-0.0023303, 5.96349, 0.000103533)
bones/27/position = Vector3(7.7871, 22.7431, -0.628749)
bones/27/rotation = Quaternion(0.227623, -0.00207102, -0.0236777, 0.973459)
bones/28/position = Vector3(-0.0565694, 6.3834, -2.02176e-05)
bones/28/rotation = Quaternion(0.136011, 0.000222886, -0.010808, 0.990648)
bones/29/position = Vector3(0.0112364, 5.18951, 4.11761e-05)
bones/29/rotation = Quaternion(0.213526, -0.00101693, -0.0215158, 0.9767)
bones/30/position = Vector3(0.0453234, 4.37233, -4.06504e-05)
bones/31/position = Vector3(-16.434, 31.0615, -0.12145)
bones/31/rotation = Quaternion(0.68985, -0.317938, 0.505282, 0.409527)
bones/32/position = Vector3(-1.22573e-05, 34.0873, 1.83794e-06)
bones/32/rotation = Quaternion(0.267799, 0.0462863, -0.312826, 0.9101)
bones/33/position = Vector3(8.547e-07, 48.2957, -1.81633e-05)
bones/33/rotation = Quaternion(0.017469, 0.0011044, -0.3226, 0.946374)
bones/34/position = Vector3(-1.25787e-05, 71.12, 1.18986e-05)
bones/34/rotation = Quaternion(0.011195, -0.233535, -0.0161426, 0.97215)
bones/35/position = Vector3(6.74174, 6.04191, 2.55781)
bones/35/rotation = Quaternion(0.0577139, -0.170863, -0.307835, 0.934191)
bones/36/position = Vector3(0.0493727, 7.52571, -1.09111e-05)
bones/36/rotation = Quaternion(0.231325, -0.0674745, 0.246114, 0.93881)
bones/37/position = Vector3(0.988447, 8.17809, -1.79844e-05)
bones/37/rotation = Quaternion(0.113642, 0.132643, -0.115446, 0.977836)
bones/38/position = Vector3(-1.03781, 6.33174, -6.67572e-06)
bones/39/position = Vector3(8.90767, 24.4465, -0.974036)
bones/39/rotation = Quaternion(0.405856, -0.00791469, 0.00197457, 0.913901)
bones/40/position = Vector3(0.00396538, 7.50239, -3.15283e-05)
bones/40/rotation = Quaternion(0.307607, -0.000385605, 0.0247048, 0.951193)
bones/41/position = Vector3(0.0146179, 6.97842, -3.73761e-06)
bones/41/rotation = Quaternion(0.157981, 9.21879e-05, 0.00979805, 0.987394)
bones/42/position = Vector3(-0.0185914, 6.19396, -1.69873e-05)
bones/43/position = Vector3(2.43325, 25.5731, -0.721194)
bones/43/rotation = Quaternion(0.380366, 0.00184866, -0.0181826, 0.924655)
bones/44/position = Vector3(-0.0145493, 8.60307, -2.56392e-05)
bones/44/rotation = Quaternion(0.231221, 0.000160949, 0.0197462, 0.972701)
bones/45/position = Vector3(0.00138187, 8.19259, 1.54368e-05)
bones/45/rotation = Quaternion(0.0476255, -3.12633e-05, 0.00472338, 0.998854)
bones/46/position = Vector3(0.0131698, 7.10842, 1.7345e-05)
bones/47/position = Vector3(-3.18938, 25.4317, -0.488043)
bones/47/rotation = Quaternion(0.294342, 0.00360707, -0.0180925, 0.955522)
bones/48/position = Vector3(-0.0198102, 7.38148, -4.00032e-07)
bones/48/rotation = Quaternion(0.179959, 0.000229753, 0.0170913, 0.983526)
bones/49/position = Vector3(-0.00488997, 6.96166, 3.82785e-05)
bones/49/rotation = Quaternion(0.0370642, 0.000114789, 0.00572692, 0.999296)
bones/50/position = Vector3(0.0247307, 6.01497, -7.16746e-05)
bones/51/position = Vector3(-8.15153, 23.0817, -0.902417)
bones/51/rotation = Quaternion(0.208634, 0.00470822, 0.0247682, 0.977669)
bones/52/position = Vector3(0.0127941, 6.12764, -8.13209e-06)
bones/52/rotation = Quaternion(0.200859, -9.8811e-06, 0.0204673, 0.979406)
bones/53/position = Vector3(-0.010273, 5.25036, 6.58943e-06)
bones/53/rotation = Quaternion(0.109442, 0.00016046, 0.0128814, 0.99391)
bones/54/position = Vector3(-0.00251889, 3.78042, 3.60608e-05)
bones/55/rotation = Quaternion(0.104542, 0.662677, 0.734095, -0.105044)
bones/56/position = Vector3(-2.53461e-06, 105.872, -8.84406e-06)
bones/56/rotation = Quaternion(-0.732034, 0.0292629, -0.138891, 0.666317)
bones/57/position = Vector3(2.93549e-06, 107.858, -6.35762e-06)
bones/57/rotation = Quaternion(0.584722, -0.169734, 0.111781, 0.785364)
bones/58/position = Vector3(-1.03784e-06, 41.4419, -2.90889e-06)
bones/58/rotation = Quaternion(0.11205, 0.00451785, 0.0400339, 0.992886)
bones/60/rotation = Quaternion(-0.0626779, 0.696232, 0.704855, 0.120463)
bones/61/position = Vector3(-3.34711e-07, 105.865, -1.8001e-05)
bones/61/rotation = Quaternion(-0.799186, 0.0607204, 0.0279366, 0.597356)
bones/62/position = Vector3(3.72907e-06, 112.577, 4.31258e-06)
bones/62/rotation = Quaternion(0.686205, 0.0935055, -0.107083, 0.713381)
bones/63/rotation = Quaternion(0.0485788, -0.00549169, -0.123198, 0.991177)
bones/64/position = Vector3(5.48363e-06, 23.6443, -4.76841e-07)

[editable path="ibn khaldun"]
