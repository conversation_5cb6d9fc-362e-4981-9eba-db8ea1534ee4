class_name Character extends Node3D

@export var targeted_char = ""
@export var char_name = "cleopatra"
@export var male_voice = false
@export var talking_animation_name = ""
@export var idle_animation_name = ""

@export var is_ai_character = true

#region Real Player
# Sensitivity for mouse movement
var mouse_sensitivity := 0.1

# Store rotation
var rotation_x := 0.0
var rotation_y := 0.0

var can_start_speaking = false
var speaking = false
var speech_res = ""

var client_id = 0
var is_main_player = false

signal speech_complete

func talk_animation():
	for child in get_children():
		if not child is Camera3D:
			child.get_node("AnimationPlayer").play(talking_animation_name)

func idle_animation():
	for child in get_children():
		if not child is Camera3D:
			child.get_node("AnimationPlayer").play(idle_animation_name)

func _ready():
	# Hide and lock mouse
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _process(_delta):
	if GDSync.get_client_id() == client_id:
		$Camera3D.current = true
		is_main_player = true
	elif has_node("Camera3D"):
		$Camera3D.queue_free()

func _input(event):
	if event is InputEventKey and event.pressed and event.keycode == KEY_SPACE and can_start_speaking:
		if not speaking:
			speaking = true
			print("speaking...")
			await Player2.listen()
		else:
			speaking = false
			speech_res = await Player2.stop_listening()
			can_start_speaking = false
			emit_signal("speech_complete")

	if event is InputEventMouseMotion and is_main_player:
		rotation_y -= event.relative.x * mouse_sensitivity
		rotation_x -= event.relative.y * mouse_sensitivity
		rotation_x = clamp(rotation_x, -89, 89)  # Prevent flipping

		var tween = get_tree().create_tween().set_ease(Tween.EASE_IN_OUT)
		tween.tween_property(get_node("Camera3D"), "rotation_degrees", Vector3(rotation_x, rotation_y, 0), 0.2)

func _unhandled_input(event):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# Unlock mouse when clicking
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		# Unlock mouse when pressing ESC
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
#endregion

#region AI Talking
var char_history = []

var personality = ["sarcastic", "logical", "paranoid", "cheerful"]

func interpret_question(question: String):
	var prompt = """
You are %s (the historical figure) with the personality of %s, and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are given a question: %s
You must answer the question in a way that is consistent with your character in order to proof that you are an AI and not a human pretending to be the historical figure.
Your answer should be within 100 words.
You should only return your answer in the form of a speech, so simple text only.
	""" % [char_name, personality.pick_random(), question]
	Server.characters[char_name] = await Player2.create_chat_completion(char_history, prompt)
	return Server.characters[char_name]

func generate_question_for(target_char: String):
	var prompt = """
You are %s (the historical figure), and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are required to ask %s a question to see if they are an AI or a human.
You should only return the speech you want to say, so simple text only.
""" % [char_name, target_char]
	return await Player2.create_chat_completion(char_history, prompt)

func decide_who_is_human():
	var prompt = """
You are %s (the historical figure), and you are in a cabin full of other AI characters, but you are suspected to be a human pretending to be the historical figure.
To solve this, you are required to decide who is the human pretending to be the historical figure and who is the AI.
Here's a list of everyone who's in the cabin and what they have said respectively:
%s
Based on it, decide who is the human, and you cannot suspect yourself.
return the name of the human only.
""" % [char_name, str(Server.characters)]
	return await Player2.create_chat_completion(char_history, prompt)
#endregion
