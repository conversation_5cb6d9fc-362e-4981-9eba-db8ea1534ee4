[gd_scene load_steps=2 format=3 uid="uid://dx00vd8c6387g"]

[ext_resource type="Script" path="res://GD-SyncTemplates/Accounts/create_account.gd" id="1_228vd"]

[node name="CreateAccount" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_228vd")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Email"

[node name="Email" type="LineEdit" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="Label2" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Username"

[node name="Username" type="LineEdit" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="Label3" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Password"

[node name="Password" type="LineEdit" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="CreateButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
text = "Create Account"

[node name="ErrorText" type="Label" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[connection signal="pressed" from="VBoxContainer/CreateButton" to="." method="create_account"]
