extends Node3D

@export var targeted_char = ""
@export var char_name = "cleopatra"
@export var male_voice = false

# Sensitivity for mouse movement
var mouse_sensitivity := 0.1

# Store rotation
var rotation_x := 0.0
var rotation_y := 0.0

var can_start_speaking = false
var speaking = false
var speech_res = ""

@export var talking_animation_name = ""
@export var idle_animation_name = ""

@onready var animation_player: AnimationPlayer = get_child(0).get_node("AnimationPlayer")

signal speech_complete

func talk_animation():
	animation_player.play(talking_animation_name)

func idle_animation():
	animation_player.play(idle_animation_name)

func _ready():
	# Hide and lock mouse
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _input(event):
	if event is InputEventKey and event.pressed and event.keycode == KEY_SPACE and can_start_speaking:
		if not speaking:
			speaking = true
			print("speaking...")
			await Player2.listen()
		else:
			speaking = false
			speech_res = await Player2.stop_listening()
			can_start_speaking = false
			emit_signal("speech_complete")

	if event is InputEventMouseMotion:
		rotation_y -= event.relative.x * mouse_sensitivity
		rotation_x -= event.relative.y * mouse_sensitivity
		rotation_x = clamp(rotation_x, -89, 89)  # Prevent flipping

		var tween = get_tree().create_tween().set_ease(Tween.EASE_IN_OUT)
		tween.tween_property(get_node("Camera3D"), "rotation_degrees", Vector3(rotation_x, rotation_y, 0), 0.2)

func _unhandled_input(event):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# Unlock mouse when clicking
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		# Unlock mouse when pressing ESC
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
	

