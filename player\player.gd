extends Node3D

# Sensitivity for mouse movement
var mouse_sensitivity := 0.1

# Store rotation
var rotation_x := 0.0
var rotation_y := 0.0

func _ready():
	# Hide and lock mouse
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _input(event):
	if event is InputEventMouseMotion:
		rotation_y -= event.relative.x * mouse_sensitivity
		rotation_x -= event.relative.y * mouse_sensitivity
		rotation_x = clamp(rotation_x, -89, 89)  # Prevent flipping

		var tween = get_tree().create_tween().set_ease(Tween.EASE_IN_OUT)
		tween.tween_property(self, "rotation_degrees", Vector3(0, rotation_y, rotation_x), 0.2)

func _unhandled_input(event):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# Unlock mouse when clicking
		Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	if event is InputEventKey and event.pressed and event.keycode == KEY_ESCAPE:
		# Unlock mouse when pressing ESC
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
